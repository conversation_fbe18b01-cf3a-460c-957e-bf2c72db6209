{"name": "ai-chat", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsgo --noEmit", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsgo --noEmit"}, "dependencies": {"@ai-sdk/deepseek": "^1.0.0-alpha.12", "@ai-sdk/google": "^2.0.0-alpha.12", "@ai-sdk/openai": "^2.0.0-alpha.12", "@ai-sdk/react": "^2.0.0-alpha.12", "@clerk/nextjs": "^6.21.0", "@clerk/themes": "^2.2.49", "@convex-dev/r2": "^0.6.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-pacer": "^0.8.0", "@tanstack/react-query": "^5.80.7", "@uidotdev/usehooks": "^2.4.1", "@vercel/analytics": "^1.5.0", "@vercel/functions": "^2.1.0", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.0-alpha.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.24.8", "convex-helpers": "^0.1.92", "ioredis": "^5.6.1", "lodash.throttle": "^4.1.1", "lucide-react": "^0.514.0", "marked": "^15.0.12", "next": "^15.3.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-shiki": "^0.7.1", "remark-gfm": "^4.0.1", "resumable-stream": "^2.2.0", "server-only": "^0.0.1", "shiki": "^3.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0", "zod": "^3.25.62", "zustand": "^5.0.5"}, "devDependencies": {"@convex-dev/eslint-plugin": "^0.0.1-alpha.4", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash.throttle": "^4.1.9", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript/native-preview": "^7.0.0-dev.20250611.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "postcss": "^8.5.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}}