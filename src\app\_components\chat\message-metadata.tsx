import { Icons } from "../ui/icons";

import { getModelData } from "@/lib/chat/models";
import type { ChatMessage } from "@/lib/types";
import { cn, format } from "@/lib/utils";

type MessageMetadataProps = {
  metadata: ChatMessage["metadata"];
  model: string;
  hiddenReasoning: boolean;
};

export function MessageMetadata({ metadata, model, hiddenReasoning }: MessageMetadataProps) {
  if (!metadata) return null;

  const modelData = getModelData(model);

  return (
    <div
      className={cn(
        "text-muted-foreground/90 flex flex-wrap items-center text-sm select-none",
        "[&>*:not(:first-child)]:before:px-1.5 [&>*:not(:first-child)]:before:content-['-']",
      )}
    >
      <div className="flex items-center justify-center gap-2">
        <Icons.provider provider={modelData?.provider} />
        <span>Generated By {modelData?.displayName}</span>
      </div>
      <span>{format.time(metadata.duration / 1000)}</span>
      <span>{format.number(metadata.totalTokens)} Tokens</span>
      {metadata.thinkingTokens > 0 && (
        <span>
          {format.number(metadata.thinkingTokens)} {hiddenReasoning ? "Hidden" : ""} Thinking Tokens
        </span>
      )}
    </div>
  );
}
